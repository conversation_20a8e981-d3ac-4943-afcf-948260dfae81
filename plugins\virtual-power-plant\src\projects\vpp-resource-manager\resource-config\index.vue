<template>
  <cet-aside class="resource-config-page">
    <template #aside>
      <!-- 左侧树形组件 -->
      <VppTree
        ref="vppTree"
        :tree-data="treeData"
        @node-click="handleNodeClick"
        @nodes-checked="handleNodesChecked"
        @tree-data-updated="handleTreeDataUpdated"
      />
    </template>
    <template #container>
      <!-- 右侧主要内容区域 -->
      <component
        :is="currentComponent"
        :key="componentKey"
        :node="selectedNode"
        :vppId="currentVppId"
        :userId="currentUserId"
        :resourceId="currentResourceId"
        :siteId="currentSiteId"
        :roomId="currentRoomId"
        :siteType="currentSiteType"
        :resourceType="currentResourceType"
        :province="currentProvince"
        :city="currentCity"
        :statData="currentNodeStats"
        @refresh-tree="handleRefreshTree"
      />
    </template>
  </cet-aside>
</template>

<script>
import VppTree from "./components/VppTree.vue";
import UserManagement from "./components/UserManagement.vue";
import ResourceManagement from "./components/ResourceManagement.vue";
import SiteManagement from "./components/SiteManagement.vue";
import VppDeviceManagement from "./components/VppDeviceManagement.vue";
import DeviceManagement from "./components/DeviceManagement.vue";
export default {
  name: "ResourceConfig",
  components: {
    VppTree,
    UserManagement,
    ResourceManagement,
    SiteManagement,
    VppDeviceManagement,
    DeviceManagement
  },
  data() {
    return {
      treeData: [],
      selectedNode: null,
      componentKey: Date.now() // 用于强制组件重新创建
    };
  },
  computed: {
    currentComponent() {
      let component;
      if (!this.selectedNode) {
        component = "UserManagement";
      } else {
        switch (this.selectedNode.type) {
          case "vpp":
            component = "UserManagement";
            break;
          case "user":
            component = "ResourceManagement";
            break;
          case "resource":
            component = "SiteManagement";
            break;
          case "site":
            component = "VppDeviceManagement";
            break;
          case "device":
            component = "DeviceManagement";
            break;
          default:
            component = "UserManagement";
        }
      }

      return component;
    },

    // 当前VPP ID
    currentVppId() {
      if (!this.selectedNode) return null;

      // 如果当前选中的是VPP节点
      if (this.selectedNode.type === "vpp") {
        return this.selectedNode.originalId;
      }

      // 从树形结构中向上查找VPP节点
      return this.findParentNodeId("vpp");
    },

    // 当前用户ID
    currentUserId() {
      if (!this.selectedNode) return null;

      // 如果当前选中的是用户节点
      if (this.selectedNode.type === "user") {
        return this.selectedNode.originalId;
      }

      // 从树形结构中向上查找用户节点
      return this.findParentNodeId("user");
    },

    // 当前资源ID
    currentResourceId() {
      if (!this.selectedNode) return null;

      // 如果当前选中的是资源节点
      if (this.selectedNode.type === "resource") {
        return this.selectedNode.originalId;
      }

      // 从树形结构中向上查找资源节点
      return this.findParentNodeId("resource");
    },

    // 当前站点ID
    currentSiteId() {
      if (!this.selectedNode) return null;

      // 如果当前选中的是站点节点
      if (this.selectedNode.type === "site") {
        return this.selectedNode.originalId;
      }

      // 从树形结构中向上查找站点节点
      return this.findParentNodeId("site");
    },

    // 当前房间ID
    currentRoomId() {
      if (!this.selectedNode) return null;
      return this.selectedNode.roomId || null;
    },

    // 当前站点类型
    currentSiteType() {
      if (!this.selectedNode) return null;
      return this.selectedNode.siteType || null;
    },

    // 当前资源类型
    currentResourceType() {
      if (!this.selectedNode) return null;
      return this.selectedNode.resourceType || null;
    },

    // 当前电厂所属省份
    currentProvince() {
      if (!this.selectedNode) return null;

      // 如果当前选中的是VPP节点，直接返回province
      if (this.selectedNode.type === "vpp") {
        return this.selectedNode.province || null;
      }

      // 如果当前选中的是用户节点，返回用户的province
      if (this.selectedNode.type === "user") {
        return this.selectedNode.province || null;
      }

      // 从树形结构中向上查找VPP节点的province
      const vppNode = this.findParentNode("vpp");
      return vppNode ? vppNode.province : null;
    },

    // 当前节点所属城市
    currentCity() {
      if (!this.selectedNode) return null;

      // 如果当前选中的是VPP节点，直接返回city
      if (this.selectedNode.type === "vpp") {
        return this.selectedNode.city || null;
      }

      // 如果当前选中的是用户节点，返回用户的city
      if (this.selectedNode.type === "user") {
        return this.selectedNode.city || null;
      }

      // 从树形结构中向上查找用户节点的city
      const userNode = this.findParentNode("user");
      return userNode ? userNode.city : null;
    },

    // 当前节点的统计数据
    currentNodeStats() {
      if (!this.selectedNode || !this.treeData || this.treeData.length === 0) {
        return { user: 0, resource: 0, site: 0, device: 0 };
      }

      // 根据当前选中节点类型计算统计数据
      const stats = this.calculateNodeStats(this.selectedNode);
      console.log(
        "计算的统计数据:",
        stats,
        "当前节点:",
        this.selectedNode.type
      );
      return stats;
    }
  },
  methods: {
    /**
     * 处理树节点点击事件
     */
    handleNodeClick(node) {
      // 更新选中节点和组件key来强制重新创建组件
      this.selectedNode = node;
      this.componentKey = Date.now();
    },

    /**
     * 处理树节点勾选事件
     */
    handleNodesChecked(nodes) {
      console.log("Checked nodes:", nodes);
    },

    /**
     * 处理树形数据更新事件
     */
    handleTreeDataUpdated(treeData) {
      this.treeData = treeData;
      console.log("Tree data updated:", treeData);
    },

    /**
     * 查找指定类型的父节点ID
     * @param {string} targetType - 目标节点类型 (vpp/user/resource/site)
     * @returns {number|null} 父节点的originalId
     */
    findParentNodeId(targetType) {
      const parentNode = this.findParentNode(targetType);
      return parentNode ? parentNode.originalId : null;
    },

    /**
     * 查找指定类型的父节点对象
     * @param {string} targetType - 目标节点类型 (vpp/user/resource/site)
     * @returns {Object|null} 父节点对象
     */
    findParentNode(targetType) {
      if (!this.selectedNode || !this.treeData) return null;

      // 层级关系：vpp -> user -> resource -> site -> device
      const hierarchy = ["vpp", "user", "resource", "site", "device"];
      const currentIndex = hierarchy.indexOf(this.selectedNode.type);
      const targetIndex = hierarchy.indexOf(targetType);

      // 如果目标类型不在当前节点的上级，返回null
      if (targetIndex >= currentIndex) return null;

      // 从当前节点开始向上查找
      let currentNodeId = this.selectedNode.tree_id;
      let currentNode = this.findNodeById(currentNodeId);

      while (currentNode && currentNode.type !== targetType) {
        if (!currentNode.pId) break;
        currentNode = this.findNodeById(currentNode.pId);
      }

      return currentNode && currentNode.type === targetType
        ? currentNode
        : null;
    },

    /**
     * 根据tree_id查找节点
     * @param {string} treeId - 树节点ID
     * @returns {Object|null} 找到的节点
     */
    findNodeById(treeId) {
      if (!this.treeData || !Array.isArray(this.treeData)) return null;

      for (const node of this.treeData) {
        if (node.tree_id === treeId) {
          return node;
        }
      }
      return null;
    },

    /**
     * 计算指定节点下的统计数据
     * @param {Object} currentNode - 当前选中的节点
     * @returns {Object} 统计数据 {user, resource, site, device}
     */
    calculateNodeStats(currentNode) {
      if (!currentNode || !this.treeData) {
        return { user: 0, resource: 0, site: 0, device: 0 };
      }

      // 找到当前节点下的所有子节点
      const childNodes = this.findChildNodes(currentNode.tree_id);

      // 根据节点类型决定需要统计的内容
      const stats = { user: 0, resource: 0, site: 0, device: 0 };

      if (currentNode.type === "vpp") {
        // VPP节点：统计用户、资源、站点、设备
        stats.user = childNodes.filter(node => node.type === "user").length;
        stats.resource = childNodes.filter(
          node => node.type === "resource"
        ).length;
        stats.site = childNodes.filter(node => node.type === "site").length;
        stats.device = childNodes.filter(node => node.type === "device").length;
      } else if (currentNode.type === "user") {
        // 用户节点：统计资源、站点、设备（用户数为0，因为用户下面没有用户）
        stats.user = 0;
        stats.resource = childNodes.filter(
          node => node.type === "resource"
        ).length;
        stats.site = childNodes.filter(node => node.type === "site").length;
        stats.device = childNodes.filter(node => node.type === "device").length;
      } else {
        // 其他节点类型暂时返回空统计
        return { user: 0, resource: 0, site: 0, device: 0 };
      }

      return stats;
    },

    /**
     * 递归查找指定节点的所有子节点
     * @param {string} parentTreeId - 父节点的tree_id
     * @returns {Array} 所有子节点数组
     */
    findChildNodes(parentTreeId) {
      if (!this.treeData || !Array.isArray(this.treeData)) return [];

      const childNodes = [];

      // 递归查找子节点的函数
      const findChildren = currentParentId => {
        this.treeData.forEach(node => {
          if (node.pId === currentParentId) {
            childNodes.push(node);
            // 递归查找该节点的子节点
            findChildren(node.tree_id);
          }
        });
      };

      findChildren(parentTreeId);
      return childNodes;
    },

    /**
     * 处理刷新树的事件
     */
    async handleRefreshTree() {
      console.log("🔄 收到刷新树的请求");
      try {
        if (this.$refs.vppTree && this.$refs.vppTree.refreshTreeData) {
          await this.$refs.vppTree.refreshTreeData();
          console.log("✅ 树形数据刷新成功");
        } else {
          console.warn("⚠️ VppTree组件或refreshTreeData方法不存在");
        }
      } catch (error) {
        console.error("❌ 刷新树形数据失败:", error);
      }
    }
  },
  mounted() {
    // 树形数据现在通过API自动加载，无需手动设置
  }
};
</script>

<style lang="scss" scoped>
.resource-config-page {
  height: 100%;
}

// cet-aside组件会自动处理布局，这里只需要设置基本样式
.resource-config-page :deep(.cet-content-aside-container) {
  background-color: var(--BG1);
  border-radius: var(--Ra);
}
</style>
