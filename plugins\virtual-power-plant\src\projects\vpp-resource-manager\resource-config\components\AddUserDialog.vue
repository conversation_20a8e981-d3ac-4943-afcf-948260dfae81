<template>
  <el-dialog
    :title="isEditMode ? $T('编辑用户') : $T('新增代理用户')"
    :visible.sync="dialogVisible"
    width="640px"
    :destroy-on-close="true"
  >
    <div class="dialog-content">
      <el-form
        ref="addUserForm"
        :model="formData"
        :rules="formRules"
        label-position="top"
      >
        <div>
          <el-row :gutter="24">
            <!-- 用户名称 -->
            <el-col :span="12">
              <el-form-item :label="$T('用户名称')" prop="userName">
                <el-input
                  v-model="formData.userName"
                  :placeholder="$T('请输入用户名称')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 联系人 -->
            <el-col :span="12">
              <el-form-item :label="$T('联系人')" prop="contactPerson">
                <el-input
                  v-model="formData.contactPerson"
                  :placeholder="$T('请输入联系人')"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <!-- 联系电话 -->
            <el-col :span="12">
              <el-form-item :label="$T('联系电话')" prop="phoneNumber">
                <el-input
                  v-model="formData.phoneNumber"
                  :placeholder="$T('请输入联系电话')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 所属区域 -->
            <el-col :span="12">
              <el-form-item :label="$T('所属区域')" prop="area">
                <el-cascader
                  v-model="formData.area"
                  :options="regionOptions"
                  :placeholder="$T('请选择区域')"
                  style="width: 100%"
                  clearable
                  :props="{
                    checkStrictly: false,
                    value: 'code',
                    label: 'name'
                  }"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <!-- 地址 -->
            <el-col :span="24">
              <el-form-item :label="$T('地址')" prop="address">
                <el-input
                  v-model="formData.address"
                  type="textarea"
                  :rows="3"
                  :placeholder="$T('请输入详细地址')"
                  :maxlength="250"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <span slot="footer">
      <el-button @click="handleClose">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="handleSave">{{ $T("保存") }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "UserDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    regionOptions: {
      type: Array,
      default: () => []
    },
    userData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: "add", // "add" 或 "edit"
      validator: value => ["add", "edit"].includes(value)
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.handleClose();
        }
      }
    },
    isEditMode() {
      return this.mode === "edit";
    }
  },
  data() {
    return {
      formData: {
        id: null,
        userName: "",
        contactPerson: "",
        phoneNumber: "",
        address: "",
        area: [],
        resourceCount: 0
      },
      formRules: {
        userName: [
          {
            required: true,
            message: this.$T("用户名称不能为空"),
            trigger: "blur"
          },
          {
            max: 100,
            message: this.$T("用户名称长度不能超过100个字符"),
            trigger: "blur"
          }
        ],
        contactPerson: [
          {
            max: 200,
            message: this.$T("联系人长度不能超过200个字符"),
            trigger: "blur"
          }
        ],
        phoneNumber: [
          {
            pattern: /^1[3-9]\d{9}$/,
            message: this.$T("请输入正确的手机号码"),
            trigger: "blur"
          }
        ],
        address: [
          {
            required: true,
            message: this.$T("地址不能为空"),
            trigger: "blur"
          },
          {
            max: 250,
            message: this.$T("地址长度不能超过250个字符"),
            trigger: "blur"
          }
        ],
        area: [
          {
            required: true,
            message: this.$T("所属区域不能为空"),
            trigger: "change"
          }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      if (val && this.isEditMode) {
        this.loadUserData();
      }
    }
  },
  methods: {
    loadUserData() {
      if (this.userData && this.userData.id) {
        this.formData = {
          id: this.userData.id,
          userName: this.userData.name || this.userData.userName || "",
          contactPerson:
            this.userData.contact || this.userData.contactPerson || "",
          phoneNumber: this.userData.phone || this.userData.phoneNumber || "",
          address: this.userData.address || "",
          area: this.userData.areaArray || [], // 使用areaArray字段，这是级联选择器需要的数组格式
          resourceCount: this.userData.count || this.userData.resourceCount || 0
        };
      }
    },
    handleClose() {
      this.resetForm();
      this.$emit("close");
    },
    async handleSave() {
      try {
        // 表单验证
        await this.$refs.addUserForm.validate();

        // 提交数据
        const [provinceId, cityId, districtId] = this.formData.area || [];
        const formDataToSave = {
          ...this.formData,
          provinceId: provinceId || null,
          cityId: cityId || null,
          districtId: districtId || null
        };
        delete formDataToSave.area; // 删除临时的 area 字段

        // 根据模式发送不同的事件
        if (this.isEditMode) {
          this.$emit("update", formDataToSave);
        } else {
          this.$emit("save", formDataToSave);
        }

        this.$message.success(this.$T("保存成功"));
        this.handleClose();
      } catch (error) {
        console.error("表单验证失败:", error);
      }
    },
    resetForm() {
      this.formData = {
        id: null,
        userName: "",
        contactPerson: "",
        phoneNumber: "",
        address: "",
        area: [],
        resourceCount: 0
      };
      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.addUserForm) {
          this.$refs.addUserForm.clearValidate();
        }
      });
    }
  }
};
</script>

<style scoped></style>
