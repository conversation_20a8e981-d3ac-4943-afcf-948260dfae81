import { httping } from "@omega/http";

// 缓存对象
const cache = {
  resourceSiteRelations: null,
  siteDeviceRelations: null,
  geographicalData: null,
  deviceMonitorRelations: null,
  availableRooms: null,
  timestamp: {
    resourceSiteRelations: 0,
    siteDeviceRelations: 0,
    geographicalData: 0,
    availableRooms: 0
  }
};

// 缓存过期时间（30分钟）
const CACHE_EXPIRE_TIME = 30 * 60 * 1000;

/**
 * 获取资源-站点类型关联关系
 * @param {boolean} forceRefresh - 是否强制刷新缓存，默认false
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: [
 *     {
 *       id: number, // 主键ID
 *       resource_type_id: number, // 资源类型ID
 *       site_type_id: number // 站点类型ID
 *     }
 *   ],
 *   msg: string,
 *   total: number
 * }
 */
export function getResourceSiteRelations(forceRefresh = false) {
  const now = Date.now();
  const cacheKey = "resourceSiteRelations";

  // 检查缓存是否有效
  if (
    !forceRefresh &&
    cache[cacheKey] &&
    now - cache.timestamp[cacheKey] < CACHE_EXPIRE_TIME
  ) {
    return Promise.resolve(cache[cacheKey]);
  }

  return httping({
    url: `/vpp/api/v1/resource-manager/base-config/resource-site-relations`,
    method: "GET"
  }).then(response => {
    // 缓存响应数据
    if (response.code === 0) {
      cache[cacheKey] = response;
      cache.timestamp[cacheKey] = now;
    }
    return response;
  });
}

/**
 * 获取电厂设备类型对应管网设备类型关联关系
 * @param {boolean} forceRefresh - 是否强制刷新缓存，默认false
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: [
 *     {
 *       id: number, // 主键ID
 *       devicetype: number, // 电厂设备类型id
 *       monitorlabel: string // 管网设备类型
 *     }
 *   ],
 *   msg: string,
 *   total: number
 * }
 */
export function getDeviceMonitorRelations(forceRefresh = false) {
  const now = Date.now();
  const cacheKey = "deviceMonitorRelations";

  // 检查缓存是否有效
  if (
    !forceRefresh &&
    cache[cacheKey] &&
    now - cache.timestamp[cacheKey] < CACHE_EXPIRE_TIME
  ) {
    return Promise.resolve(cache[cacheKey]);
  }

  return httping({
    url: `/vpp/api/v1/resource-manager/base-config/device-monitor-relations`,
    method: "GET"
  }).then(response => {
    // 缓存响应数据
    if (response.code === 0) {
      cache[cacheKey] = response;
      cache.timestamp[cacheKey] = now;
    }
    return response;
  });
}

/**
 * 获取站点-设备类型关联关系
 * @param {boolean} forceRefresh - 是否强制刷新缓存，默认false
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: [
 *     {
 *       id: number, // 主键ID
 *       device_type_id: number, // 设备类型ID
 *       site_iype_id: number // 站点类型ID（注意：API文档中此字段名有拼写错误）
 *     }
 *   ],
 *   msg: string,
 *   total: number
 * }
 */
export function getSiteDeviceRelations(forceRefresh = false) {
  const now = Date.now();
  const cacheKey = "siteDeviceRelations";

  // 检查缓存是否有效
  if (
    !forceRefresh &&
    cache[cacheKey] &&
    now - cache.timestamp[cacheKey] < CACHE_EXPIRE_TIME
  ) {
    return Promise.resolve(cache[cacheKey]);
  }

  return httping({
    url: `/vpp/api/v1/resource-manager/base-config/site-device-relations`,
    method: "GET"
  }).then(response => {
    // 缓存响应数据
    if (response.code === 0) {
      cache[cacheKey] = response;
      cache.timestamp[cacheKey] = now;
    }
    return response;
  });
}

/**
 * 获取所有地理数据
 * @param {boolean} forceRefresh - 是否强制刷新缓存，默认false
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: [
 *     {
 *       id: number,     // 地区ID
 *       code: numer,   // 地区编码
 *       name: string,   // 地区名称
 *       modelLabel:string， //  地区类型
         children: Array // 子地区列表
 *     }
 *   ]
 *   },
 *   msg: string,
 *   total: number
 * }
 */
export function getGeographicalData(forceRefresh = false) {
  const now = Date.now();
  const cacheKey = "geographicalData";

  // 检查缓存是否有效
  if (
    !forceRefresh &&
    cache[cacheKey] &&
    now - cache.timestamp[cacheKey] < CACHE_EXPIRE_TIME
  ) {
    return Promise.resolve(cache[cacheKey]);
  }

  return httping({
    url: `/vpp/api/v1/resource-manager/base-config/geographical-data`,
    method: "GET"
  }).then(response => {
    // 缓存响应数据
    if (response.code === 0) {
      cache[cacheKey] = response;
      cache.timestamp[cacheKey] = now;
    }
    return response;
  });
}

/**
 * 上传图片
 * @param {File} file - 图片文件对象
 * @param {string} fileName - 文件名称（可选）
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: {
 *     imagePath: string, // 图片路径，可直接存储到设备picture字段
 *     fileName: string,  // 文件名
 *     fileSize: number   // 文件大小
 *   },
 *   msg: string
 * }
 */
export function uploadImage(file, fileName = null) {
  const formData = new FormData();
  formData.append("file", file);

  const params = {};
  if (fileName) {
    params.fileName = fileName;
  }

  return httping({
    url: `/vpp/api/v1/resource-manager/base-config/images/upload`,
    method: "POST",
    data: formData,
    params,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
}

/**
 * 下载图片
 * @param {string} imagePath - 图片路径
 * @returns {Promise} 返回Promise对象，响应为图片文件流
 */
export function downloadImage(imagePath) {
  return httping({
    url: `/vpp/api/v1/resource-manager/base-config/images/download/${imagePath}`,
    method: "GET",
    responseType: "blob"
  });
}

/**
 * 根据房间查询管网设备
 * @param {Object} queryData - 查询参数
 * @param {number} queryData.roomId - 房间ID
 * @param {string} queryData.roomType - 房间类型
 * @param {number} queryData.siteId - 站点id
 * @param {Array<string>} queryData.deviceTypes - 管网设备类型列表
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: {
 *     [modelLabel]: [  // 管网类型作为key
 *       {
 *         id: string,           // 设备ID
 *         name: string,         // 设备名称
 *         modelLabel: string,   // 管网设备类型（battery,pcs...）
 *       }
 *     ]
 *   },
 *   msg: string,
 *   total: number
 * }
 */
export function getMonitorDevicesByRoom(queryData) {
  return httping({
    url: `/vpp/api/v1/resource-manager/base-config/monitor-devices/by-room`,
    method: "POST",
    data: queryData
  });
}

/**
 * 根据模型查询枚举
 * @param {String} modelLabel - 枚举模型名称
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data:[
 *     {
 *       id: string,           // 枚举ID
 *       text: string,   // 枚举名称
 *       modelLabel: string,   // 枚举模型
 *     }
 *   ],
 *   msg: string,
 *   total: number
 * }
 */
export function getEnumsByLabel(queryData) {
  const { modelLabel, ...params } = queryData;
  return httping({
    url: `/vpp/api/v1/resource-manager/base-config/enums/${modelLabel}`,
    method: "GET",
    params: params
  });
}

/**
 * 查询可用房间
 * @param {boolean} forceRefresh - 是否强制刷新缓存，默认false
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: {
 *     [roomType]: [
 *       {
 *         id: number,           // 房间ID
 *         roomName: string,     // 房间名称
 *         roomType: number,     // 房间类型
 *         roomCode: string,     // 房间编码
 *         description: string,  // 房间描述
 *         status: number        // 房间状态
 *       }
 *     ]
 *   },
 *   msg: string,
 *   total: number
 * }
 */
export function getAvailableRooms(forceRefresh = false) {
  const now = Date.now();
  const cacheKey = "availableRooms";

  // 检查缓存是否有效
  if (
    !forceRefresh &&
    cache[cacheKey] &&
    now - cache.timestamp[cacheKey] < CACHE_EXPIRE_TIME
  ) {
    return Promise.resolve(cache[cacheKey]);
  }

  return httping({
    url: `/vpp/api/v1/resource-manager/base-config/available-rooms`,
    method: "GET"
  }).then(response => {
    // 缓存响应数据
    if (response.code === 0) {
      cache[cacheKey] = response;
      cache.timestamp[cacheKey] = now;
    }
    return response;
  });
}

/**
 * 清除基础配置缓存
 * @param {string} type - 缓存类型，可选值：'resourceSiteRelations', 'siteDeviceRelations', 'geographicalData', 'availableRooms', 'all'
 */
export function clearBaseConfigCache(type = "all") {
  if (type === "all") {
    cache.resourceSiteRelations = null;
    cache.siteDeviceRelations = null;
    cache.geographicalData = null;
    cache.availableRooms = null;
    cache.timestamp.resourceSiteRelations = 0;
    cache.timestamp.siteDeviceRelations = 0;
    cache.timestamp.geographicalData = 0;
    cache.timestamp.availableRooms = 0;
  } else if (cache[type] !== undefined) {
    cache[type] = null;
    cache.timestamp[type] = 0;
  }
}

/**
 * 获取缓存状态信息
 * @returns {Object} 缓存状态信息
 */
export function getCacheStatus() {
  const now = Date.now();
  return {
    resourceSiteRelations: {
      cached: !!cache.resourceSiteRelations,
      timestamp: cache.timestamp.resourceSiteRelations,
      expired:
        cache.timestamp.resourceSiteRelations > 0 &&
        now - cache.timestamp.resourceSiteRelations >= CACHE_EXPIRE_TIME
    },
    siteDeviceRelations: {
      cached: !!cache.siteDeviceRelations,
      timestamp: cache.timestamp.siteDeviceRelations,
      expired:
        cache.timestamp.siteDeviceRelations > 0 &&
        now - cache.timestamp.siteDeviceRelations >= CACHE_EXPIRE_TIME
    },
    geographicalData: {
      cached: !!cache.geographicalData,
      timestamp: cache.timestamp.geographicalData,
      expired:
        cache.timestamp.geographicalData > 0 &&
        now - cache.timestamp.geographicalData >= CACHE_EXPIRE_TIME
    },
    availableRooms: {
      cached: !!cache.availableRooms,
      timestamp: cache.timestamp.availableRooms,
      expired:
        cache.timestamp.availableRooms > 0 &&
        now - cache.timestamp.availableRooms >= CACHE_EXPIRE_TIME
    }
  };
}

/**
 * 根据省份编码获取电厂类型列表
 *
 * @param provinceCode 省份编码
 * @return 电厂类型列表
 */
export function getPowerplantTypesByProvince(provinceCode) {
  return httping({
    url: `/vpp/api/v1/resource-manager/base-config/province-powerplant-relations/${provinceCode}`,
    method: "GET"
  });
}
